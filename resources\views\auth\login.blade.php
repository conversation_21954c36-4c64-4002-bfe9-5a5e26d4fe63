@extends('layouts.auth')

@section('title', 'เข้าสู่ระบบ - SoloShop')

@section('content')
<form method="POST" action="{{ route('login') }}" id="loginForm">
    @csrf

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>ไม่สามารถเข้าสู่ระบบได้</strong>
            <div class="mt-1">กรุณาตรวจสอบอีเมลและรหัสผ่านอีกครั้ง</div>
        </div>
    @endif

    <div class="form-floating">
        <div class="input-icon">
            <i class="fas fa-envelope"></i>
        </div>
        <input id="email"
               type="email"
               class="form-control @error('email') is-invalid @enderror"
               name="email"
               value="{{ old('email') }}"
               required
               autocomplete="email"
               autofocus
               placeholder=" ">
        <label for="email" class="form-label">อีเมล</label>
        @error('email')
            <div class="invalid-feedback d-block">
                {{ $message }}
            </div>
        @enderror
    </div>

    <div class="form-floating">
        <div class="input-icon">
            <i class="fas fa-lock"></i>
        </div>
        <input id="password"
               type="password"
               class="form-control @error('password') is-invalid @enderror"
               name="password"
               required
               autocomplete="current-password"
               placeholder=" ">
        <button type="button" class="password-toggle" onclick="togglePassword()">
            <i class="fas fa-eye" id="passwordIcon"></i>
        </button>
        <label for="password" class="form-label">รหัสผ่าน</label>
        @error('password')
            <div class="invalid-feedback d-block">
                {{ $message }}
            </div>
        @enderror
    </div>

    <div class="form-check">
        <input class="form-check-input"
               type="checkbox"
               name="remember"
               id="remember"
               {{ old('remember') ? 'checked' : '' }}>
        <label class="form-check-label" for="remember">
            <i class="fas fa-check-circle me-1"></i>
            จดจำการเข้าสู่ระบบ
        </label>
    </div>

    <div class="d-grid mb-4">
        <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
            <i class="fas fa-sign-in-alt me-2"></i>
            เข้าสู่ระบบ
        </button>
    </div>

    @if (Route::has('password.request'))
        <div class="text-center mb-3">
            <a href="{{ route('password.request') }}" class="forgot-password">
                <i class="fas fa-key me-1"></i>
                ลืมรหัสผ่าน?
            </a>
        </div>
    @endif
</form>

@if (!Route::has('register'))
    <div class="text-center">
        <div class="alert alert-light border-0 py-3">
            <div class="mb-2">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <small class="text-muted">
                    หากยังไม่มีบัญชี กรุณาติดต่อผู้ดูแลระบบ
                </small>
            </div>
        </div>

        <div class="test-credentials">
            <div class="d-flex align-items-center justify-content-center mb-2">
                <span class="badge me-2">ทดสอบระบบ</span>
                <i class="fas fa-flask text-primary"></i>
            </div>
            <div class="row text-center">
                <div class="col-6">
                    <small class="text-muted d-block">อีเมล</small>
                    <small class="fw-bold"><EMAIL></small>
                </div>
                <div class="col-6">
                    <small class="text-muted d-block">รหัสผ่าน</small>
                    <small class="fw-bold">admin123</small>
                </div>
            </div>
        </div>
    </div>
@endif

@push('scripts')
<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('passwordIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
    }
}

document.getElementById('loginForm').addEventListener('submit', function() {
    const loginBtn = document.getElementById('loginBtn');
    loginBtn.classList.add('btn-loading');
    loginBtn.disabled = true;
});

// Auto-fill demo credentials
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');

    // Add click handler to test credentials
    document.querySelector('.test-credentials').addEventListener('click', function() {
        emailInput.value = '<EMAIL>';
        passwordInput.value = 'admin123';

        // Trigger floating label animation
        emailInput.dispatchEvent(new Event('input'));
        passwordInput.dispatchEvent(new Event('input'));

        // Focus on submit button
        document.getElementById('loginBtn').focus();
    });
});
</script>
@endpush
@endsection
