@extends('layouts.auth')

@section('title', 'เข้าสู่ระบบ - SoloShop')

@section('content')
<form method="POST" action="{{ route('login') }}">
    @csrf

    @if($errors->any())
        <div class="alert alert-danger">
            <strong>ไม่สามารถเข้าสู่ระบบได้</strong>
            <div class="mt-1">กรุณาตรวจสอบอีเมลและรหัสผ่านอีกครั้ง</div>
        </div>
    @endif

    <div class="mb-3">
        <label for="email" class="form-label">อีเมล</label>
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-user"></i>
            </span>
            <input id="email"
                   type="email"
                   class="form-control @error('email') is-invalid @enderror"
                   name="email"
                   value="{{ old('email') }}"
                   required
                   autocomplete="email"
                   autofocus
                   placeholder="<EMAIL>">
        </div>
        @error('email')
            <div class="invalid-feedback d-block">
                {{ $message }}
            </div>
        @enderror
    </div>

    <div class="mb-3">
        <label for="password" class="form-label">รหัสผ่าน</label>
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-lock"></i>
            </span>
            <input id="password"
                   type="password"
                   class="form-control @error('password') is-invalid @enderror"
                   name="password"
                   required
                   autocomplete="current-password"
                   placeholder="รหัสผ่านของคุณ">
        </div>
        @error('password')
            <div class="invalid-feedback d-block">
                {{ $message }}
            </div>
        @enderror
    </div>

    <div class="mb-4">
        <div class="form-check">
            <input class="form-check-input"
                   type="checkbox"
                   name="remember"
                   id="remember"
                   {{ old('remember') ? 'checked' : '' }}>
            <label class="form-check-label" for="remember">
                จดจำการเข้าสู่ระบบ
            </label>
        </div>
    </div>

    <div class="d-grid mb-3">
        <button type="submit" class="btn btn-primary btn-lg">
            เข้าสู่ระบบ
        </button>
    </div>

    @if (Route::has('password.request'))
        <div class="text-center">
            <a href="{{ route('password.request') }}" class="text-decoration-none small">
                ลืมรหัสผ่าน?
            </a>
        </div>
    @endif
</form>

@if (!Route::has('register'))
    <div class="text-center mt-4">
        <div class="alert alert-light border-0 py-2">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                หากยังไม่มีบัญชี กรุณาติดต่อผู้ดูแลระบบ
            </small>
        </div>
        <div class="mt-2">
            <small class="text-muted">
                <strong>ทดสอบระบบ:</strong> <EMAIL> / admin123
            </small>
        </div>
    </div>
@endif
@endsection
