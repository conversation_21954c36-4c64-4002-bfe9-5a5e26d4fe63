

<?php $__env->startSection('title', 'เข้าสู่ระบบ - SoloShop'); ?>

<?php $__env->startSection('content'); ?>
<form method="POST" action="<?php echo e(route('login')); ?>">
    <?php echo csrf_field(); ?>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger">
            <strong>ไม่สามารถเข้าสู่ระบบได้</strong>
            <div class="mt-1">กรุณาตรวจสอบอีเมลและรหัสผ่านอีกครั้ง</div>
        </div>
    <?php endif; ?>

    <div class="mb-3">
        <label for="email" class="form-label">อีเมล</label>
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-user"></i>
            </span>
            <input id="email"
                   type="email"
                   class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                   name="email"
                   value="<?php echo e(old('email')); ?>"
                   required
                   autocomplete="email"
                   autofocus
                   placeholder="<EMAIL>">
        </div>
        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="invalid-feedback d-block">
                <?php echo e($message); ?>

            </div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="mb-3">
        <label for="password" class="form-label">รหัสผ่าน</label>
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-lock"></i>
            </span>
            <input id="password"
                   type="password"
                   class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                   name="password"
                   required
                   autocomplete="current-password"
                   placeholder="รหัสผ่านของคุณ">
        </div>
        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="invalid-feedback d-block">
                <?php echo e($message); ?>

            </div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="mb-4">
        <div class="form-check">
            <input class="form-check-input"
                   type="checkbox"
                   name="remember"
                   id="remember"
                   <?php echo e(old('remember') ? 'checked' : ''); ?>>
            <label class="form-check-label" for="remember">
                จดจำการเข้าสู่ระบบ
            </label>
        </div>
    </div>

    <div class="d-grid mb-3">
        <button type="submit" class="btn btn-primary btn-lg">
            เข้าสู่ระบบ
        </button>
    </div>

    <?php if(Route::has('password.request')): ?>
        <div class="text-center">
            <a href="<?php echo e(route('password.request')); ?>" class="text-decoration-none small">
                ลืมรหัสผ่าน?
            </a>
        </div>
    <?php endif; ?>
</form>

<?php if(!Route::has('register')): ?>
    <div class="text-center mt-4">
        <div class="alert alert-light border-0 py-2">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                หากยังไม่มีบัญชี กรุณาติดต่อผู้ดูแลระบบ
            </small>
        </div>
        <div class="mt-2">
            <small class="text-muted">
                <strong>ทดสอบระบบ:</strong> <EMAIL> / admin123
            </small>
        </div>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/auth/login.blade.php ENDPATH**/ ?>