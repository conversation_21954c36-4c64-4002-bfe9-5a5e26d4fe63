<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'เข้าสู่ระบบ - SoloShop'); ?></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .auth-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            overflow: hidden;
            max-width: 420px;
            width: 100%;
        }

        .auth-header {
            background: white;
            color: #2c3e50;
            padding: 2.5rem 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid #f1f3f4;
        }

        .auth-header h1 {
            margin: 0;
            font-size: 1.75rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .auth-header p {
            margin: 0.5rem 0 0 0;
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 400;
        }

        .auth-body {
            padding: 2rem;
        }

        .form-control {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.2s ease;
            background-color: #f8f9fa;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
            background-color: white;
        }

        .btn-primary {
            background: #0d6efd;
            border: 1px solid #0d6efd;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: #0b5ed7;
            border-color: #0a58ca;
            transform: translateY(-1px);
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .auth-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: #6c757d;
            font-size: 0.875rem;
            background-color: #f8f9fa;
        }

        .auth-footer a {
            color: #0d6efd;
            text-decoration: none;
            font-weight: 500;
        }

        .auth-footer a:hover {
            text-decoration: underline;
        }

        .logo-container {
            margin-bottom: 1rem;
        }

        .logo-container i {
            font-size: 2.5rem;
            color: #0d6efd;
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            border: 1px solid #f5c6cb;
            background-color: #f8d7da;
            color: #721c24;
        }

        .alert-light {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #6c757d;
        }

        .invalid-feedback {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .form-check-label {
            color: #495057;
            font-size: 0.9rem;
        }

        .input-group-text {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-right: none;
            color: #6c757d;
        }

        .input-group .form-control {
            border-left: none;
        }

        .input-group .form-control:focus {
            border-left: none;
            box-shadow: none;
        }

        .input-group:focus-within .input-group-text {
            border-color: #0d6efd;
            color: #0d6efd;
        }

        .btn-lg {
            padding: 0.875rem 1.5rem;
            font-size: 1.1rem;
        }

        @media (max-width: 576px) {
            .auth-container {
                margin: 0.5rem;
                border-radius: 8px;
            }

            .auth-header {
                padding: 2rem 1.5rem 1rem;
            }

            .auth-body {
                padding: 1.5rem;
            }
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <div class="logo-container">
                <i class="fas fa-store-alt"></i>
            </div>
            <h1>SoloShop</h1>
            <p>เข้าสู่ระบบจัดการ</p>
        </div>

        <div class="auth-body">
            <?php echo $__env->yieldContent('content'); ?>
        </div>

        <div class="auth-footer">
            <small>&copy; <?php echo e(date('Y')); ?> SoloShop</small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/layouts/auth.blade.php ENDPATH**/ ?>